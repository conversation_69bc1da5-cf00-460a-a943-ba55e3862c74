### 批量导出操作模板 HTTP测试文件
### 测试ValuedAddedDeliveryOrderController.batchExportImportOperationTemplate方法
### 支持三种操作类型的Excel模板导出测试
###
### 操作类型说明：
### - DELIVERY: 交付操作模板
### - SUPPLEMENT_DELIVERY: 补充交付附件操作模板
### - DEDUCTION: 扣款操作模板
###
### 测试场景覆盖：
### 1. 交付操作模板导出
### 2. 补充交付附件操作模板导出
### 3. 扣款操作模板导出
### 4. 参数验证测试（空列表、无效操作类型等）

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImZiMDI2ZjkyLWU4YzQtNDFiZC1hMWU4LWM4ZjVmM2ZiNTBhNCIsInVzZXJuYW1lIjoiYWRtaW4ifQ.bKQWhFmWnUaNkYiJC2tkaXv1UHxGZDdF-cV5lflkTXVSwVWULa4YKW8rNVzL8rnNn320TCUDGbz47U33bG3Crw

### ========================================
### 1. 交付操作模板导出测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "SW2508051430001A1C",
    "SW2508051430002A1C",
    "SW2508051430003A1C"
  ],
  "operation": "DELIVERY"
}

### ========================================
### 2. 补充交付附件操作模板导出测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "SW2508051430001A1C",
    "SW2508051430002A1C"
  ],
  "operation": "SUPPLEMENT_DELIVERY"
}

### ========================================
### 3. 扣款操作模板导出测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "SW2508051430001A1C",
    "SW2508051430004A1C",
    "SW2508051430005A1C"
  ],
  "operation": "DEDUCTION"
}

### ========================================
### 4. 单个交付单导出测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "SW2508051430001A1C"
  ],
  "operation": "DELIVERY"
}

### ========================================
### 5. 参数验证测试 - 空交付单列表
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [],
  "operation": "DELIVERY"
}

### ========================================
### 6. 参数验证测试 - 缺少操作类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "SW2508051430001A1C"
  ]
}

### ========================================
### 7. 参数验证测试 - 无效的交付单编号
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "INVALID_ORDER_NO_001",
    "INVALID_ORDER_NO_002"
  ],
  "operation": "DELIVERY"
}

### ========================================
### 8. 大批量导出测试（10个交付单）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "SW2508051430001A1C",
    "SW2508051430002A1C",
    "SW2508051430003A1C",
    "SW2508051430004A1C",
    "SW2508051430005A1C",
    "SW2508051430006A1C",
    "SW2508051430007A1C",
    "SW2508051430008A1C",
    "SW2508051430009A1C",
    "SW2508051430010A1C"
  ],
  "operation": "SUPPLEMENT_DELIVERY"
}

### ========================================
### 9. 混合状态交付单导出测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchExportImportOperationTemplate
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNoList": [
    "SW2508051430001A1C",
    "SW2508051430002A1C",
    "SW2508051430003A1C",
    "SW2508051430004A1C",
    "SW2508051430005A1C"
  ],
  "operation": "DEDUCTION"
}

### ========================================
### 测试说明：
### 1. 所有测试都会返回Excel文件下载
### 2. 成功的请求会直接下载Excel文件到浏览器
### 3. 失败的请求会返回JSON错误信息
### 4. 请确保测试的交付单编号在数据库中存在
### 5. 建议先运行单个交付单测试，确认基本功能正常
### ========================================
