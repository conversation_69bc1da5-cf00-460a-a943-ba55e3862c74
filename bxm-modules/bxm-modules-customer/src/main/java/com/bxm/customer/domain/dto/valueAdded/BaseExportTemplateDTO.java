package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * 导出模板基础DTO
 *
 * 包含所有导出模板的共同字段：
 * 交付单编号、企业名、信用代码、备注、附件文件名、附件文件夹名、库存表文件名
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@SuperBuilder
public abstract class BaseExportTemplateDTO {

    /** 交付单编号 */
    @Excel(name = "交付单编号", sort = 1)
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /** 企业名 */
    @Excel(name = "企业名", sort = 2)
    @ApiModelProperty(value = "企业名")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码", sort = 3)
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /** 备注 */
    @Excel(name = "备注", sort = 10)
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 附件文件名 */
    @Excel(name = "附件文件名", sort = 11)
    @ApiModelProperty(value = "附件文件名")
    private String attachmentFileName;

    /** 附件文件夹名 */
    @Excel(name = "附件文件夹名", sort = 12)
    @ApiModelProperty(value = "附件文件夹名")
    private String attachmentFolderName;

    /** 库存表文件名 */
    @Excel(name = "库存表文件名", sort = 13)
    @ApiModelProperty(value = "库存表文件名")
    private String stockFileName;
}
