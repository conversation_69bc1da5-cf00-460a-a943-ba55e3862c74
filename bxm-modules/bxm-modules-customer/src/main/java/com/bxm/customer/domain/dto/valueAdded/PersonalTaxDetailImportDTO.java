package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 个税明细导入DTO
 *
 * 用于Excel导入个税明细数据，继承基类的共同字段（方式、姓名、身份证号、手机号、备注、社保基数、
 * 养老、失业、工伤、医疗、生育），并添加个税特有字段：应发工资、公积金个人缴存金额、其他等
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@ApiModel("个税明细导入DTO")
public class PersonalTaxDetailImportDTO extends BaseDetailExportDTO {

    /** 行号（用于导入时错误定位） */
    @ApiModelProperty(value = "行号")
    private Integer rowNumber;

    /** 应发工资 */
    @Excel(name = "应发工资", sort = 12)
    @ApiModelProperty(value = "应发工资")
    private String grossSalary;

    /** 公积金个人缴存金额 */
    @Excel(name = "公积金个人缴存金额", sort = 13)
    @ApiModelProperty(value = "公积金个人缴存金额")
    private String housingFundPersonalAmount;

    /** 其他 */
    @Excel(name = "其他", sort = 14)
    @ApiModelProperty(value = "其他")
    private String other;
}
